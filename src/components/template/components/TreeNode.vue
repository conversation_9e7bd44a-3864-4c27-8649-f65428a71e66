<template>
  <div class="tree-list">
    <div v-for="item in innerDataList" :key="item.id" class="tree-item">
      <div class="tree-header">
        <div class="tree-checkbox">
          <div v-if="item.children?.length" @click="handleShowToggle(item)">
            <ChevronDownIcon v-if="!isOpen" style="margin-right: 5px" />
            <ChevronUpIcon v-else />
          </div>

          <div>
            <t-checkbox
              v-if="item.parentId == 0"
              v-model="item.checked"
              :style="{ marginLeft: item.children?.length ? '' : '20px' }"
            ></t-checkbox>
          </div>
        </div>
        <div class="tree-title">
          <div class="tree-name">{{ item.name }}</div>
          <div v-if="item.parentId == 0" class="tree-tag">
            <t-tag
              v-if="item.free == 2"
              shape="round"
              theme="success"
              variant="outline"
              size="large"
              >可试读</t-tag
            >
            <t-tag
              v-if="item.free == 1"
              shape="round"
              theme="default"
              variant="outline"
              size="large"
              >不可试读</t-tag
            >

            <t-tag
              v-if="item.chapterStatus == 1"
              shape="round"
              theme="primary"
              variant="outline"
              size="large"
              >已提交</t-tag
            >
            <t-tag
              v-if="item.chapterStatus == 2"
              shape="round"
              theme="default"
              variant="outline"
              size="large"
              >已通过</t-tag
            >
            <t-tag
              v-if="item.chapterStatus == 3"
              shape="round"
              theme="danger"
              size="large"
              variant="outline"
              >已驳回</t-tag
            >
            <!-- <span
              v-if="item.chapterStatus == 3"
              class="error-icon"
              @click="handleRejectedShow(item)"
              >!</span
            > -->
          </div>
        </div>
      </div>
      <div v-if="item.parentId == 0" class="tree-content">
        <div class="tree-author">编写者:{{ item.editor }}</div>
        <div class="tree-buttons">
          <t-button
            v-if="
              (item.chapterStatus == 0 || item.chapterStatus == 3) &&
              item.publishStatus == 1 &&
              ( item.currentStepId == 1 || auditState == null || auditState == 3) &&
              (curUserPermissions.permissions.includes(1) ||
                curUserPermissions.permissions.includes(2) ||
                item.isEditor == true)
            "
            theme="default"
            size="large"
            variant="text"
            @click="handleEditContent(item)"
          >
            <template #default>
              <i class="iconfont-edit"></i>
              编写内容
            </template>
          </t-button>
          <t-button
            v-if="
              (item.chapterStatus == 0 || item.chapterStatus == 3) &&
              item.publishStatus == 1 &&
              ( item.currentStepId == 1 || auditState == null || auditState == 3) &&
              (curUserPermissions.permissions.includes(1) ||
                curUserPermissions.permissions.includes(2) ||
                item.isEditor == true)
            "
            theme="default"
            size="large"
            variant="text"
            @click="importChapter(item)"
          >
            <template #icon>
              <i class="iconfont-edit iconfont-import" />
              导入
            </template>
          </t-button>
          <t-button
            v-if="
              curUserPermissions.permissions.includes(1) ||
              curUserPermissions.permissions.includes(2) ||
              curUserPermissions.isEditor ||
              item.isEditor == true ||
              item.isViewer == true
            "
            theme="default"
            size="large"
            variant="text"
            @click="jumpReader(item)"
          >
            <template #icon>
              <i class="iconfont-edit iconfont-reader" />
              阅读器预览
            </template>
          </t-button>

          <!-- <t-button
            v-if="
              curUserPermissions.permissions.includes(1) ||
              curUserPermissions.permissions.includes(2) ||
              curUserPermissions.isEditor ||
              item.isEditor == true ||
              item.isViewer == true
            "
            theme="default"
            size="large"
            variant="text"
            @click="handlePreview(item)"
          >
            <template #icon>
              <i class="iconfont-edit iconfont-search" />
              预览
            </template>
          </t-button> -->
          <t-button
            v-if="
              (item.chapterStatus == 0 || item.chapterStatus == 3) &&
              (curUserPermissions.permissions.includes(1) ||
                curUserPermissions.permissions.includes(2) ||
                item.isEditor == true)
            "
            theme="default"
            size="large"
            variant="text"
            @click="submitChapter(item)"
          >
            <template #icon>
              <i class="iconfont-edit iconfont-submit" />
              提交
            </template>
          </t-button>
          <t-button
            v-if="
              item.publishStatus == 1 &&
              ( item.currentStepId == 1 || auditState == null || auditState == 3) &&
              (curUserPermissions.permissions.includes(1) ||
                curUserPermissions.permissions.includes(2) ||
                curUserPermissions.isEditor) &&
              (item.chapterStatus == 0 || item.chapterStatus == 3)
            "
            theme="default"
            size="large"
            variant="text"
            @click="editChapter(item)"
          >
            <template #icon>
              <i class="iconfont-edit iconfont-editer" />
              编辑
            </template>
          </t-button>
          <t-button
            v-if="
              item.publishStatus == 1 &&
              (curUserPermissions.permissions.includes(1) ||
                curUserPermissions.permissions.includes(2) ||
                curUserPermissions.isEditor ||
                item.isEditor == true ||
                item.isViewer == true)
            "
            theme="default"
            size="large"
            variant="text"
            @click="handlePrint(item)"
          >
            <template #icon>
              <i class="iconfont-edit iconfont-print" />
              打印
            </template>
          </t-button>
          <t-button
            v-if="
              item.chapterStatus == 1 &&
              (curUserPermissions.permissions.includes(1) ||
                curUserPermissions.permissions.includes(2) ||
                item.isEditor == true)
            "
            theme="default"
            size="large"
            variant="text"
            @click="handleCancel(item)"
          >
            <template #icon>
              <i class="iconfont-edit iconfont-cancel" />
              取消
            </template>
          </t-button>
          <t-button
            v-if="
              item.chapterStatus == 2 &&
              ( item.currentStepId == 1 || auditState == null || auditState == 3) &&
              item.revoked == 2 &&
              (curUserPermissions.permissions.includes(1) ||
                curUserPermissions.permissions.includes(2) ||
                item.isEditor == true)
            "
            theme="default"
            size="large"
            variant="text"
            @click="handleRevoked(item)"
          >
            <template #icon>
              <i class="iconfont-edit iconfont-cancel" />
              撤销
            </template>
          </t-button>
        </div>
      </div>
      <div v-if="item.parentId == 0" class="tree-line">
        <span style="margin-right: 10px">完成度</span>
        <t-progress :percentage="item.completeRate" style="width: 270px" />
        <div
          v-if="
            (curUserPermissions.permissions.includes(1) ||
              curUserPermissions.permissions.includes(2) ||
              item.isEditor == true) &&
            (item.chapterStatus == 0 || item.chapterStatus == 3)
          "
          class="tree-set"
          @click="handleSettingShow(item)"
        >
          <!-- && item.completeRate != 100 -->
          设置
        </div>
      </div>
      <TreeNode
        v-if="isOpen && item.children && item.children.length > 0"
        :data-list="isOpen ? item.children : []"
      />
    </div>

    <CharpterMange
      ref="chapterMange"
      :is-free="props.isFree"
      :master-flag="masterFlag"
      :book-organize="bookOrganize"
      @refresh="refresh"
    />

    <modal
      v-model:visible="completeRateDialogVisible"
      align-center
      destroy-on-close
      header="设置进度"
      width="500"
      :confirm-btn="null"
      :cancel-btn="null"
    >
      <div class="tree-setting">
        <div class="tree-setting-title">
          <span>当前设置进度值:</span><strong>{{ form.completeRate }}%</strong>
        </div>
        <div style="padding: 20px">
          <t-slider v-model="form.completeRate" :min="0" :max="100" />
        </div>

        <div class="tree-dialog-footer">
          <t-button
            theme="default"
            style="margin-right: 10px"
            @click="completeRateDialogVisible = false"
            >取消</t-button
          >
          <t-button @click="handleCompleteRateSave"> 设置 </t-button>
        </div>
      </div>
    </modal>
    <modal
      v-model:visible="rejectedDialogVisible"
      align-center
      destroy-on-close
      header="驳回原因"
      width="500"
      :confirm-btn="null"
      :cancel-btn="null"
    >
      <div class="tree-setting">
        <div class="tree-setting-title">
          <span style="font-weight: bold">{{ form.chapterName }}</span>
        </div>
        <div class="tree-setting-title">
          <span style="color: red">{{
            form.auditType == 1 ? '章节提交申请已驳回' : '章节撤销申请已驳回'
          }}</span>
        </div>
        <div
          class="tree-setting-title"
          style="background-color: #eef5fd; padding: 10px"
        >
          <span>{{ form.remark }}</span>
        </div>
      </div>
    </modal>
    <modal
      v-model:visible="revokedDialogVisible"
      align-center
      destroy-on-close
      header="撤销"
      width="500"
      :confirm-btn="null"
      :cancel-btn="null"
    >
      <div style="margin-bottom: 10px">撤销理由：</div>
      <t-textarea
        v-model="form.revokedReason"
        :maxlength="200"
        :autosize="{ minRows: 4, maxRows: 6 }"
        placeholder="请输入内容"
      />

      <div class="dialog-footer">
        <t-button
          theme="default"
          style="margin-right: 10px"
          show-word-limit
          @click="revokedDialogVisible = false"
          >取消</t-button
        >
        <t-button @click="handleCompleteRevoked"> 确定 </t-button>
      </div>
    </modal>
    <t-dialog
      v-model:visible="upload.open"
      header="章节导入"
      width="400px"
      destroy-on-close
      :confirm-btn="null"
      @cancel="upload.open = false"
    >
      <t-upload
        ref="uploadRef"
        :limit="1"
        accept=".docx"
        :headers="upload.headers"
        :action="upload.url + '?chapterId=' + upload.chapterId"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        draggable
      />
      仅允许导入docx格式文件。
      <div class="worn-text">
        文件内，第一行必须是一级标题， 一级标题只能有一个
      </div>
    </t-dialog>
  </div>
</template>

<script setup name="TreeNode">
import { ChevronDownIcon, ChevronUpIcon } from 'tdesign-icons-vue-next'
import { DialogPlugin, MessagePlugin } from 'tdesign-vue-next'
import { useRouter } from 'vue-router'

import {
  queryRejectedReasonInfo,
  revokedChapterApply,
} from '@/api/book/bookChapterAuditLog'
import { updateChapter } from '@/api/book/chapter'
import { getToken } from '@/request/token.js'

import CharpterMange from './charpterMange.vue'
const { proxy } = getCurrentInstance()
const props = defineProps({
  dataList: {
    type: Array,
    required: true,
  },
  curUserPermissions: {
    type: Object,
    default: {
      permissions: [],
      isEditor: false,
      isAuthor: false,
      bookId: null,
    },
  },
  isFree: {
    type: Boolean,
    default: false,
  },
  masterFlag: {
    type: Number,
    default: 1,
  },
  bookOrganize: {
    type: Number,
    default: 1,
  },
  auditState: {
    type: Number,
    default: null,
  }
})

const router = useRouter()
const emit = defineEmits(['refresh', 'editChapter'])

const isOpen = ref(false)
const form = ref({})
const completeRateDialogVisible = ref(false)
const rejectedDialogVisible = ref(false)
const revokedDialogVisible = ref(false)
const innerDataList = ref(props.dataList || [])
const curChapterId = ref('')
const upload = reactive({
  open: false,
  isUploading: false,
  chapterId: null,
  headers: {
    Authorization: `Bearer ${getToken()}`,
    Language: sessionStorage.getItem('Language'),
  },
  url: `${import.meta.env.VITE_APP_BASE_API}/book/chapter/importChapter`,
})

//#region 监听器相关

// TODO bug 监听不到二级数据
watch(
  () => props.dataList,
  (value) => {
    innerDataList.value = value
  },
  {
    deep: true,
  },
)

//#endregion
//#region 生命周期相关

// onMounted(() => {
//   innerDataList.value = props.dataList;
// })

//#endregion
//#region 操作相关

// 跳转阅读器
function jumpReader(item) {
  const { chapterId } = item
  const { bookId } = item
  window.open(
    `${`${import.meta.env.VITE_READER_PREVIEW_URL}?k=${bookId}&cid=${chapterId}`}`,
  )
}

// 预览章节
function handlePreview(item) {
  router.push({
    path: '/bookPreview',
    query: {
      chapterId: item.chapterId,
      bookId: item.bookId,
      bookPreviewType: 2,
      formType: 1,
    },
  })
}

// 打印
function handlePrint(item) {
  router.push({
    path: '/chapterDetail',
    query: {
      chapterId: item.chapterId,
      formType: 1,
      bookId: item.bookId,
      optType: 2,
    },
  })
}

// 编辑章节
function editChapter(item) {
  proxy.$refs['chapterMange'].show(item.bookId, item.chapterId)
}

// 编辑进度条
function handleSettingShow(item) {
  form.value = {
    chapterId: item.chapterId,
    completeRate: item.completeRate,
  }
  completeRateDialogVisible.value = true
}

// 保存进度条
function handleCompleteRateSave() {
  updateChapter(form.value).then((res) => {
    emit('refresh')
    MessagePlugin.success('更新进度条成功')
    completeRateDialogVisible.value = false
  })
}

// 打开驳回理由弹窗
function handleRejectedShow(item) {
  queryRejectedReasonInfo(item.chapterId).then((res) => {
    form.value = res.data || {}
    rejectedDialogVisible.value = true
  })
}

// 撤销
function handleRevoked(item) {
  form.value = {
    chapterId: item.chapterId,
    auditType: 2,
    bookId: item.bookId,
    revokedReason: '',
  }
  revokedDialogVisible.value = true
}

// 确定撤销
function handleCompleteRevoked() {
  revokedChapterApply(form.value).then((res) => {
    refresh()
    MessagePlugin.success('撤销成功')
    revokedDialogVisible.value = false
  })
}

// 取消提交章节
function handleCancel(item) {
  const confirmDia = DialogPlugin.confirm({
    header: '取消章节',
    body: '确定进行取消操作？确认后章节回退到创建状态。',
    theme: 'warning',
    attach: 'body',
    placement: 'center',
    showInAttachedElement: false,
    onCancel: () => {
      confirmDia.destroy()
    },
    onClose: () => {
      confirmDia.destroy()
    },
    onConfirm() {
      updateChapter({
        chapterId: item.chapterId,
        chapterStatus: 0,
      }).then((res) => {
        refresh()
        confirmDia.destroy()
        MessagePlugin.success('取消成功')
      })
    },
  })
}

// 提交章节
function submitChapter(item) {
  const confirmDia = DialogPlugin.confirm({
    header: '提交章节',
    body: '确定要提交该章节内容？',
    theme: 'warning',
    onCancel: () => {
      confirmDia.destroy()
    },
    onClose: () => {
      confirmDia.destroy()
    },
    onConfirm() {
      updateChapter({
        chapterId: item.chapterId,
        chapterStatus: 1,
      })
        .then((res) => {
          refresh()
          confirmDia.destroy()
          MessagePlugin.success('提交成功')
        })
        .catch((err) => {
          confirmDia.destroy()
        })
    },
  })
}

// 刷新数据
function refresh() {
  emit('refresh')
}

// 切换展示展示子菜单
function handleShowToggle() {
  isOpen.value = !isOpen.value
}

// 编辑章节内容
function handleEditContent(item) {
  emit('editChapter', item)
}

// 对外暴露选中结果
function handleSelect() {
  const selectdataList = innerDataList.value.filter((item) => item.checked)
  return selectdataList
}

// 对外暴露全选
function handleCheckAll() {
  innerDataList.value.forEach((item) => {
    item.checked = true
  })
}

// 对外暴露取消全选
function handleCancelCheckAll() {
  innerDataList.value.forEach((item) => {
    item.checked = false
  })
}

// 文件上传中处理
function handleFileUploadProgress(event, file, fileList) {
  upload.isUploading = true
}

// 文件上传成功处理
function handleFileSuccess(response, file, fileList) {
  upload.open = false
  upload.isUploading = false
  MessagePlugin.success('上传成功')
  const confirmDia = DialogPlugin.confirm({
    header: '导入Word',
    body: '导入操作已成功完成，请您移步至任务中心查看详细的导入进度信息。在此期间，为了确保数据处理的完整性和准确性，请勿进行保存操作。',
    theme: 'warning',
    cancelBtn: '留在本页',
    confirmBtn: '前往任务中心',
    onClose: () => {
      confirmDia.destroy()
    },
    onConfirm() {
      router.push('/pages/taskCenter')
      confirmDia.destroy()
    },
  })
}

// 导入章节
function importChapter(item) {
  upload.open = true
  upload.chapterId = item.chapterId
}

//#endregion

//#region 暴露函数相关

defineExpose({
  handleSelect,
  handleCheckAll,
  handleCancelCheckAll,
})

//#endregion
</script>

<style scoped lang="less">
:deep(.t-upload__single-name) {
  width: 50%;
}
.worn-text {
  margin-top: 10px;
  margin-right: 7px;
  padding: 10px;
  border-radius: 5px;
  color: #ff6262;
  background-color: #ffeded;
  border: 2px solid #ffc8c8;
}
.tree-list {
  .tree-header {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    .tree-checkbox {
      display: flex;
      align-items: center;
    }
    .tree-title {
      font-size: 16px;
      font-weight: bold;
      padding-left: 20px;
      display: flex;
      align-items: center;

      .tree-name {
        padding-right: 20px;
      }

      .tree-tag {
        .t-tag {
          margin-right: 10px;
        }
        .error-icon {
          width: 20px;
          height: 20px;
          display: inline-flex;
          align-items: center;
          justify-content: center;
          background: #f56c6c;
          border-radius: 50%;
          color: #fff;
          font-size: 12px;
        }
      }
    }
  }

  .tree-content {
    display: flex;
    justify-content: space-between;
    font-size: 14px;

    .tree-author {
      color: #666;
      padding-left: 50px;
    }

    .tree-buttons {
      display: flex;
      align-content: center;

      .t-button {
        color: #0966b4;
        :global(.t-button__text) {
          position: relative;
          z-index: 1;
          display: inline-flex;
          justify-content: center;
          align-items: center;
        }
      }
      .iconfont-edit {
        display: inline-flex;
        width: 16px;
        height: 16px;
        background: url('@/assets/images/edit.svg') no-repeat;
        background-size: contain;
        margin-right: 5px;
      }

      .iconfont-import {
        background: url('@/assets/images/import.svg') no-repeat;
      }

      .iconfont-search {
        background: url('@/assets/images/search.svg') no-repeat;
      }

      .iconfont-submit {
        background: url('@/assets/images/submit.svg') no-repeat;
      }

      .iconfont-editer {
        background: url('@/assets/images/editer.svg') no-repeat;
      }
      .iconfont-reader {
        background: url('@/assets/images/search.svg') no-repeat;
      }

      .iconfont-print {
        background: url('@/assets/images/print.svg') no-repeat;
      }

      .iconfont-cancel {
        background: url('@/assets/images/cancel.svg') no-repeat;
      }
    }
  }

  .tree-item {
    padding: 20px 10px;

    &:hover {
      background: #f6f6f6;

      cursor: pointer;
    }
  }

  .tree-line {
    font-size: 14px;
    display: flex;
    padding-left: 50px;

    .tree-set {
      margin-left: 27px;
      color: #0966b4;
      cursor: pointer;
    }

    .demo-progress .el-progress--line {
      margin-bottom: 15px;
      max-width: 600px;
    }
  }

  .tree-setting {
    .tree-setting-title {
      color: #999;

      span {
        color: #666;
      }

      strong {
        font-size: 16px;
        padding: 0 3px;
      }

      margin-bottom: 10px;
    }
    :global(.tree-dialog-footer) {
      display: flex;
      justify-content: flex-end;
      margin-top: 30px;
    }
  }

  :global(.dialog-footer) {
    display: flex;
    justify-content: flex-end;
    margin-top: 30px;
  }
}
</style>
