<template>
  <menus-button ico="image" menu-type="dropdown" :text="t('insert.image.text')" :huge="isHuge" :trigger="isTirgger"
    :disabled="isDisabled" :tooltip="isTooltip" :placement="isPlacement">
    <template #dropmenu>
      <t-dropdown-menu>
        <t-dropdown-item v-for="item in options" :key="item.value" class="umo-text-image-menu" :value="item.value">
          <span>{{ item.label }}</span>
          <t-dropdown-menu style="margin-left: 100px">
            <t-dropdown-item @click="imageChange(item as imageOption)">
              {{ t('insert.image.local') }}
            </t-dropdown-item>
            <t-dropdown-item @click="setDialog(item as imageOption)">
              {{ t('insert.image.gallery') }}
            </t-dropdown-item>
          </t-dropdown-menu>
        </t-dropdown-item>
      </t-dropdown-menu>
    </template>
  </menus-button>
  <ResourceLibrary v-model:visible="show" :file-type="'1'" :multiple="multiple"
    @insert-by-resource="insertByResource" />
</template>

<script setup lang="ts">
import { chooseFile } from '@/utils/file'
const { editor, batchInsert } = useStore()
const props = defineProps({
  isHuge: {
    type: Boolean,
    default: false,
  },
  isTirgger: {
    type: String,
    default: 'click',
  },
  isDisabled: {
    type: Boolean,
    default: false,
  },
  isTooltip: {
    type: [String, Boolean],
    default: undefined,
  },
  isPlacement: {
    type: String,
    default: 'bottom-left',
  },
})
const resourceRef = ref()
const imageType = ref('')
const multiple = ref(false)
interface imageOption {
  label: string
  type?: string
}
const options = [
  {
    label: t('insert.image.layoutImages'),
    type: 'layoutImages',
    children: [
      { label: '本地', value: 1 },
      { label: '图库', value: 2 },
    ],
  },

  { label: t('insert.image.inlineImages'), type: 'icon' },
  { label: t('insert.image.imageGallery'), type: 'imageGallery' },
]

// { label: t('insert.image.inlineImages'), type: 'inlineImages' },
let image = $ref<imageOption | undefined>()

const insertByResource = (file) => {
  if (imageType.value == 'layoutImages') {
    // editor.value
    //   ?.chain()
    //   .focus()
    //   .setImageLayout({
    //     src: file.fileUrl,
    //     size: file.fileSize,
    //     imageTitle: file.name,
    //     name: file.name,
    //     width: file.width,
    //     height: file.height,
    //   })
    //   .run()
    batchInsert(file, 'imageLayout', (f) => ({
      src: f.fileUrl,
      name: f.name,
      size: f.fileSize,
      imageTitle: f.name,
      width: f.width,
      height: f.height,
    }), true, (f) => f.name || '图片标题')
  }
  // if (imageType.value == 'inlineImages') {
  //   editor.value
  //     ?.chain()
  //     .focus()
  //     .setImageInLine({
  //       src: file.fileUrl,
  //       size: file.fileSize,
  //       imageTitle: file.name,
  //       name: file.name,
  //     })
  //     .run()
  // }
  if (imageType.value == 'icon') {
    editor.value
      ?.chain()
      .focus()
      .setImageIcon({
        src: file.fileUrl,
        size: file.fileSize,
        imageTitle: file.name,
        name: file.name,
      })
      .run()
  }
  if (imageType.value == 'imageGallery') {
    editor.value
      ?.chain()
      .focus()
      .insertImageGallery({
        imgList: file.map((f) => ({
          src: f.fileUrl,
          name: f.name,
          size: f.fileSize,
          imageTitle: f.name,
        })),
      })
      .run()
  }
}
const imageChange = (item: imageOption) => {
  if (item.type == 'layoutImages') {
    chooseFile((files) => {
      // editor.value
      //   ?.chain()
      //   .focus()
      //   .setImageLayout({
      //     src: file.fileUrl,
      //     name: file.originName,
      //     size: file.fileSize,
      //     imageTitle: file.originName,
      //   })
      //   .run()
      batchInsert(files, 'imageLayout', (file) => ({
        src: file.fileUrl,
        name: file.originName,
        size: file.fileSize,
        imageTitle: file.originName,
      }), true, (f) => f.originName || '图片标题')
    }, true)
  }
  if (item.type == 'inlineImages') {
    chooseFile((file) => {
      editor.value
        ?.chain()
        .focus()
        .setImageInLine({
          src: file.fileUrl,
          size: file.fileSize,
          imageTitle: file.originName,
          name: file.originName,
        })
        .run()
    })
  }
  if (item.type == 'icon') {
    chooseFile((file) => {
      editor.value
        ?.chain()
        .focus()
        .setImageIcon({
          src: file.fileUrl,
          size: file.fileSize,
          imageTitle: file.originName,
          name: file.originName,
        })
        .run()
    })
  }
  if (item.type == 'imageGallery') {
    chooseFile((files) => {
      editor.value
        ?.chain()
        .focus()
        .insertImageGallery({
          imgList: files.map((f) => ({
            src: f.fileUrl,
            name: f.originName,
            size: f.fileSize,
            imageTitle: f.originName,
          })),
        })
        .run()
    }, true)
  }
  image = item
}

const clearFormat = () => {
  editor.value?.chain().focus().unsetHighlight().run()
  editor.value?.chain().focus().unsetColor().run()
  image = {}
}

const show = ref(false)
const setDialog = (item: imageOption) => {
  imageType.value = item.type
  if (item.type == 'imageGallery' || item.type == 'layoutImages') {
    multiple.value = true
  } else {
    multiple.value = false
  }
  show.value = true
}
</script>
<style lang="less" scoped>
:deep(.umo-popup) {
  margin-left: -20px !important;
}
</style>
