<template>
  <node-view-wrapper :id="node.attrs.id" as="span" class="umo-node-bubble">
    <t-dropdown trigger="click">
      <u class="umo-node-title"><span v-if="node.attrs.bubbleType != '2'" :contenteditable="false">{{
        node.attrs.bubbleTitle
      }}</span>
        <Twitch v-if="
          node.attrs.bubbleType === '2' &&
          node.attrs.bubbleIconSelect === 'twitch'
        " :color="node.attrs.bubbleBgColor" class="bubble-icon" />

        <SearchAlt v-if="
          node.attrs.bubbleType === '2' &&
          node.attrs.bubbleIconSelect === 'search-alt'
        " :color="node.attrs.bubbleBgColor" class="bubble-icon" />

        <Interactive v-if="
          node.attrs.bubbleType === '2' &&
          node.attrs.bubbleIconSelect === 'interactive'
        " :color="node.attrs.bubbleBgColor" class="bubble-icon" />

        <Editbubble v-if="
          node.attrs.bubbleType === '2' &&
          node.attrs.bubbleIconSelect === 'editbubble'
        " :color="node.attrs.bubbleBgColor" class="bubble-icon" />

        <CommentAltDots v-if="
          node.attrs.bubbleType === '2' &&
          node.attrs.bubbleIconSelect === 'comment-alt-dots'
        " :color="node.attrs.bubbleBgColor" class="bubble-icon" />
      </u>
      <template #dropdown>
        <t-dropdown-item :value="2" @click="openImageViewer">
          <div style="display: flex; align-items: center">
            <Edit2Icon />
            <span style="margin-left: 5px">{{
              t('insert.bubble.bubbleEdit')
            }}</span>
          </div>
        </t-dropdown-item>
        <t-dropdown-item :value="4" @click="deleteNode">
          <div style="display: flex; align-items: center">
            <DeleteIcon />
            <span style="margin-left: 5px">{{
              t('insert.bubble.bubbleDelete')
            }}</span>
          </div>
        </t-dropdown-item>
      </template>
    </t-dropdown>
    <t-dialog v-model:visible="bubbleShow" :header="t('insert.bubble.text')" width="600" :footer="false" mode="modeless"
      :draggable="true" :show-overlay="true" :z-index="1001" @close="onClose">
      <t-form ref="formValidatorStatus" :data="formData" :rules="rules" :label-width="120" @reset="onReset"
        @submit="onConfirm">
        <t-form-item name="bubbleType">
          <t-radio-group v-model="formData.bubbleType" default-value="1">
            <t-radio-button value="1">{{
              t('insert.bubble.bubbleText')
            }}</t-radio-button>
            <t-radio-button value="2">{{
              t('insert.bubble.bubbleIcon')
            }}</t-radio-button>
          </t-radio-group>
        </t-form-item>

        <t-form-item v-if="formData.bubbleType == 1" :label="t('insert.bubble.bubbleTitle')" name="bubbleTitle">
          <t-input v-model="formData.bubbleTitle" :placeholder="t('insert.bubble.bubbleTitlePlaceholder')"
            show-limit-number></t-input>
        </t-form-item>
        <t-form-item :label="t('insert.bubble.bubbleUrl')" name="bubbleUrl">
          <t-input v-model="formData.bubbleUrl" :placeholder="t('insert.bubble.bubbleUrlPlaceholder')"></t-input>
        </t-form-item>
        <t-form-item :label="t('insert.bubble.colorgroup')" name="bubbleFontColor">
          <t-popup placement="right-bottom" attach="body" :destroy-on-close="false">
            <div class="umo-color-picker-more" style="display: flex; align-items: center; cursor: pointer;">
              <div
                :style="`margin-right:10px;border:1px solid #ddd;width:20px;height:20px;border-radius:5px;background:${formData.bubbleFontColor || '#333'};margin-right:10px;`">
              </div>
              <div class="umo-color-picker-more-menu"
                :style="`border-bottom: 3px solid ${formData.bubbleFontColor || '#333'};}`">
                <span v-text="t('insert.bubble.bubbleFontColor')"></span>
              </div>
            </div>
            <template #content>
              <div style="padding: 10px">
                <color-picker :default-color="defaultColor" @change="bubbleColorChange" />
              </div>
            </template>
          </t-popup>
          <t-popup placement="right-bottom" attach="body" :destroy-on-close="false">
            <div class="umo-color-picker-more" style="display: flex; align-items: center; cursor: pointer">
              <div
                :style="`border:1px solid #ddd;width:20px;height:20px;border-radius:5px;background:${formData.bubbleBackgroundColor};margin-right:10px;`">
              </div>
              <div class="umo-color-picker-more-menu"
                :style="`border-bottom: 3px solid ${formData.bubbleBackgroundColor};}`">
                <span v-text="t('insert.bubble.bubbleBackgroundColor')"></span>
              </div>
            </div>
            <template #content>
              <div style="padding: 10px">
                <color-picker :default-color="defaultColor" @change="bubbleBackgroundColorChange" />
              </div>
            </template>
          </t-popup>
          <t-popup v-if="formData.bubbleType == 2" placement="right-bottom" attach="body" :destroy-on-close="false">
            <div class="umo-color-picker-more" style="display: flex; align-items: center; cursor: pointer">
              <div
                :style="`border:1px solid #ddd;width:20px;height:20px;border-radius:5px;background:${formData.bubbleBgColor};margin-right:10px;`">
              </div>
              <div class="umo-color-picker-more-menu" :style="`border-bottom: 3px solid ${formData.bubbleBgColor};}`">
                <span v-text="t('insert.bubble.bubbleBgColor')"></span>
              </div>
            </div>
            <template #content>
              <div style="padding: 10px">
                <color-picker :default-color="defaultColor" @change="bubbleBgColorChange" />
              </div>
            </template>
          </t-popup>
        </t-form-item>

        <t-form-item v-if="formData.bubbleType == 2" :label="t('insert.bubble.bubbleIconSelect')"
          name="bubbleIconSelect">
          <t-radio-group v-model="formData.bubbleIconSelect" size="large" @change="changeIcon">
            <t-radio-button v-for="item in imgList" :key="item.name" :value="item.name">
              <img :src="item.url" style="
                width: 20px;
                height: 20px;
                display: flex;
                align-items: center;
                justify-content: center;
              " />
            </t-radio-button>
          </t-radio-group>
        </t-form-item>
        <t-form-item :label="t('insert.bubble.bubbleContent')" name="bubbleContent" required-mark>
          <t-textarea v-model="formData.bubbleContent" :placeholder="t('insert.bubble.bubbleContentPlaceholder')"
            :autosize="{ minRows: 4, maxRows: 4 }" :maxlength="1000" show-limit-number></t-textarea>
          <!-- <wangEditor
            v-if="bubbleShow"
            v-model:text-content="formData.bubbleContent"
            :height-props="300"
            @update-text-content="doUpdateItemContent"
          /> -->
        </t-form-item>
        <t-form-item>
          <t-tag theme="warning">注意：气泡组件不支持跨段落进行内容选择，若强行操作，将会导致段落合并</t-tag>
        </t-form-item>
        <div class="footer-btns">
          <t-button theme="primary" type="submit">{{ t('insert.bubble.submit') }}
          </t-button>
        </div>
      </t-form>
    </t-dialog>
  </node-view-wrapper>

</template>

<script setup>
import { nodeViewProps, NodeViewWrapper } from '@tiptap/vue-3'
import { DeleteIcon, Edit2Icon } from 'tdesign-icons-vue-next'
import { defineProps, ref } from 'vue'

import commentAltDotsIcon from '@/assets/icons/comment-alt-dots.svg'
import editbubbleIcon from '@/assets/icons/editbubble.svg'
import interactiveIcon from '@/assets/icons/interactive.svg'
import searchAltIcon from '@/assets/icons/search-alt.svg'
import twitchIcon from '@/assets/icons/twitch.svg'

import CommentAltDots from './svg/commentAltDots.vue'
import Editbubble from './svg/editbubble.vue'
import Interactive from './svg/interactive.vue'
import SearchAlt from './svg/searchAlt.vue'
import Twitch from './svg/twitch.vue'

const { node, updateAttributes } = defineProps(nodeViewProps)
const { editor } = useStore()
const bubbleShow = ref(false)
const formData = ref({
  bubbleType: node.attrs.bubbleType,
  bubbleTitle: node.attrs.bubbleTitle,
  bubbleUrl: node.attrs.bubbleUrl,
  bubbleContent: node.attrs.bubbleContent,
  bubbleFontColor: node.attrs.bubbleFontColor,
  bubbleBackgroundColor: node.attrs.bubbleBackgroundColor,
  bubbleIconSelect: node.attrs.bubbleIconSelect,
  bubbleBgColor: node.attrs.bubbleBgColor,
})


const defaultColor = ref('#333')
const rules = {
  bubbleTitle: [
    {
      required: true,
      message: t('insert.bubble.bubbleTitlePlaceholder'),
      type: 'error',
      trigger: 'blur',
    },
  ],
  bubbleContent: [
    {
      required: true,
      message: t('insert.bubble.bubbleContentPlaceholder'),
      type: 'error',
      trigger: 'blur',
    },
  ],
}

const imgList = [
  {
    name: 'twitch',
    url: twitchIcon,
  },
  {
    name: 'search-alt',
    url: searchAltIcon,
  },
  {
    name: 'interactive',
    url: interactiveIcon,
  },
  {
    name: 'editbubble',
    url: editbubbleIcon,
  },
  {
    name: 'comment-alt-dots',
    url: commentAltDotsIcon,
  },
]

const openImageViewer = () => {
  localStorage.setItem('bubbleShowL', '1')
  formData.value = {
    bubbleType: node.attrs.bubbleType,
    bubbleTitle: node.attrs.bubbleTitle,
    bubbleUrl: node.attrs.bubbleUrl,
    bubbleContent: node.attrs.bubbleContent,
    bubbleFontColor: node.attrs.bubbleFontColor,
    bubbleBackgroundColor: node.attrs.bubbleBackgroundColor,
    bubbleIconSelect: node.attrs.bubbleIconSelect,
    bubbleBgColor: node.attrs.bubbleBgColor,
  }
  bubbleShow.value = true
}

const deleteNode = (e) => {
  if (localStorage.getItem('bubbleShowL')) {
    localStorage.removeItem('bubbleShowL')
  }
  editor.value?.commands.deleteSelectionNode()
}

const onConfirm = () => {
  if (localStorage.getItem('bubbleShowL')) {
    localStorage.removeItem('bubbleShowL')
  }
  if (formData.value.bubbleUrl != '') {
    if (
      formData.value.bubbleUrl.startsWith('http://') ||
      formData.value.bubbleUrl.startsWith('https://') ||
      formData.value.bubbleUrl.startsWith('ftp://') ||
      formData.value.bubbleUrl.startsWith('ftps://') ||
      formData.value.bubbleUrl.startsWith('www.') ||
      formData.value.bubbleUrl.startsWith('mailto://')
    ) {
      if (formData.value.bubbleUrl.startsWith('www.')) {
        formData.value.bubbleUrl = `https://${formData.value.bubbleUrl}`
      }
    } else {
      useMessage('error', '请填写正确的链接地址')
      return false
    }
  }
  console.log(formData.value)
  if (!formData.value.bubbleContent) return useMessage('error', '请填写描述')
  updateAttributes({
    bubbleType: formData.value.bubbleType,
    bubbleTitle: formData.value.bubbleTitle,
    bubbleUrl: formData.value.bubbleUrl,
    bubbleContent: formData.value.bubbleContent,
    bubbleFontColor: formData.value.bubbleFontColor,
    bubbleBackgroundColor: formData.value.bubbleBackgroundColor,
    bubbleIconSelect: formData.value.bubbleIconSelect,
    bubbleBgColor: formData.value.bubbleBgColor,
  })
  bubbleShow.value = false
}
const onClose = () => {
  if (localStorage.getItem('bubbleShowL')) {
    localStorage.removeItem('bubbleShowL')
  }
  bubbleShow.value = false
}

const changeIcon = (e) => {
  formData.value.bubbleIconSelect = e
}

const bubbleColorChange = (value) => {
  formData.value.bubbleFontColor = value
}

const bubbleBgColorChange = (value) => {
  formData.value.bubbleBgColor = value
}

const bubbleBackgroundColorChange = (value) => {
  formData.value.bubbleBackgroundColor = value
}
</script>

<style lang="less" scoped>
.umo-node-bubble {
  .umo-button--variant-text {
    font: normal;
  }

  .ghost-button {
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 0;
    border-bottom: 4px solid var(--umo-primary-color);

    &:hover {
      border-left: none;
      border-right: none;
      border-top: none;
    }
  }

  .bubble-icon {
    display: inline-block;
    min-width: 20px;
    aspect-ratio: 1 / 1;
    /* 保持宽高比 */
  }

  .responsive-svg {
    vertical-align: middle;
    /* 使 SVG 与文字垂直对齐 */
  }

  .umo-node-title {
    text-indent: 0 !important;
    color: #333;
  }


}

.umo-color-picker-more-menu {
  margin-right: 10px;
}

.footer-btns {
  display: flex;
  justify-content: flex-end;
  padding: 10px 0;
}
</style>
