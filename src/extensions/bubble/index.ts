import {
  type CommandProps,
  mergeAttributes,
  Node,
  type NodeViewProps,
} from '@tiptap/core'
import { VueNodeViewRenderer } from '@tiptap/vue-3'

import NodeView from './node-view.vue'
declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    setBubbleInline: {
      setBubbleInline: (options: any) => ReturnType
    }
  }
}

export default Node.create({
  name: 'bubbleInline',
  inline: true,
  group: 'inline',
  atom: true,
  addAttributes() {
    return {
      vnode: {
        default: true,
      },
      id: {
        default: null,
      },

      bubbleTitle: {
        default: '',
      },
      bubbleContent: {
        default: '',
      },
      bubbleUrl: {
        default: '',
      },
      bubbleType: {
        default: '1',
      },
      bubbleFontColor: {
        default: '#fff',
      },
      bubbleBackgroundColor: {
        default: '#333',
      },
      bubbleIconSelect: {
        default: 'bubble-icon',
      },
      bubbleBgColor: {
        default: '#333',
      },
    }
  },
  parseHTML() {
    return [{ tag: 'bubbleInline' }]
  },
  renderHTML({ HTMLAttributes }) {
    return [
      'bubbleInline',
      mergeAttributes(this.options.HTMLAttributes, HTMLAttributes),
    ]
  },
  addNodeView() {
    return VueNodeViewRenderer(NodeView as Component<NodeViewProps>)
  },
  addCommands() {
    return {
      setBubbleInline:
        (options) =>
        ({ commands, editor }: CommandProps) => {
          let op = false
          console.log(options)
          op = commands.insertContent({
            type: this.name,
            attrs: options,
          })

          // 默认值
          return op
        },
    }
  },
})
