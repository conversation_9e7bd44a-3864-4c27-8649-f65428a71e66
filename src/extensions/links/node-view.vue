<!--
 * @Author: 矫建武 <EMAIL>
 * @Date: 2024-11-16 15:09:44
 * @LastEditTime: 2025-02-25 10:34:31
 * @FilePath: \dutp-editor\src\extensions\links\node-view.vue
 * @Description: 
 * 
 * @file: https://github.com/OBKoro1/koro1FileHeader/wiki/%E5%AE%89%E8%A3%85%E5%92%8C%E5%BF%AB%E9%80%9F%E4%B8%8A%E6%89%8B
-->
<template>
  <node-view-wrapper as="span" class="umo-node-links">
    <t-dropdown trigger="click">
      <u class="umo-node-title"
        ><span>
        <span class="link-text-with-icon">{{ node.attrs.title }}</span>
<!--        <icon name="link" style="transform: scale(0.9);display: inline-block;" />-->
      </span
        >
      </u>
      <template #dropdown>
        <t-dropdown-item :value="1" @click="openLink">
          <div style="display: flex; align-items: center">
            <FileSearchIcon />
            <span style="margin-left: 5px">{{
              t('insert.link.linkOpen')
            }}</span>
          </div>
        </t-dropdown-item>
        <t-dropdown-item :value="2" @click="editLink">
          <div style="display: flex; align-items: center">
            <Edit2Icon />
            <span style="margin-left: 5px">{{
              t('insert.link.linkEdit')
            }}</span>
          </div>
        </t-dropdown-item>

        <t-dropdown-item :value="4" @click="deleteNode">
          <div style="display: flex; align-items: center">
            <DeleteIcon />
            <span style="margin-left: 5px">{{ t('insert.link.linkDel') }}</span>
          </div>
        </t-dropdown-item>
      </template>
    </t-dropdown>
    <Web
      :dialog-visible="isShow.web"
      :close="() => (isShow.web = false)"
      :text="node.attrs.title"
      :href="node.attrs.href"
      :update="(v) => update(v, 'web')"
    />
    <BookNode
      :dialog-visible="isShow.bookNode"
      :close="() => (isShow.bookNode = false)"
      :text="node.attrs.title"
      :href="node.attrs.href"
      :update="(v) => update(v, 'bookNode')"
    />
    <Overlapping
      :dialog-visible="isShow.overlapping"
      :close="() => (isShow.overlapping = false)"
      :text="node.attrs.title"
      :href="node.attrs.href"
      :attrs="node.attrs.attrs"
      :quote-type="node.attrs.quoteType"
      :update="(v) => update(v, 'overlapping')"
    />

    <ResourceLibrary
      v-model:visible="isShow.resourceLibraryShow"
      :title="node.attrs.title"
      :is-link="true"
      @insert-by-resource="resourceLibraryUpdate"
    />

    <modal
      :visible="overlappingShow"
      :footer="false"
      :header="node.attrs.title"
      width="1000px"
      @close="overlappingShow = false"
    >
      <div class="umo-node-links-content">
        <img
          v-if="
            node.attrs.quoteType ===
              'imageLayout,image,imageIcon,imageInLine' ||
            validQuoteTypes.includes(node.attrs.quoteType)
          "
          :src="node.attrs.href"
          style="width: 100%; height: 100%"
        />
        <iframe
          v-if="
            node.attrs.quoteType == 'resourceCover_0' ||
            node.attrs.quoteType == 'resourceCover_1' ||
            node.attrs.quoteType == 'resourceCover_3' ||
            node.attrs.quoteType == 'resourceCover_4' ||
            node.attrs.quoteType == 'resourceCover_5'
          "
          :src="node.attrs.attrs.url"
          style="width: 1000px; height: 600px; border: none"
        ></iframe>

        <audio
          v-if="node.attrs.quoteType == 'audio'"
          :src="node.attrs.href"
          controls
        ></audio>

        <video
          v-if="node.attrs.quoteType == 'video'"
          :src="node.attrs.href"
          controls
          autoplay
          style="width: 100%"
        ></video>
      </div>
    </modal>

    <modal
      :visible="resourceLibraryShow"
      :footer="false"
      width="1000px"
      :header="node.attrs.attrs.name"
      @close="resourceLibraryShow = false"
    >
      <img
        v-if="node.attrs.attrs.fileType == '1'"
        :src="node.attrs.attrs.fileUrl"
        style="width: 100%; height: 100%"
      />

      <audio
        v-if="node.attrs.attrs.fileType == '2'"
        :src="node.attrs.attrs.fileUrl"
        controls
      ></audio>

      <video
        v-if="node.attrs.attrs.fileType == '3'"
        :src="node.attrs.attrs.fileUrl"
        controls
        autoplay
      ></video>

      <iframe
        v-if="
          node.attrs.attrs.fileType == '4' ||
          node.attrs.attrs.fileType == '5' ||
          node.attrs.attrs.fileType == '6' ||
          node.attrs.attrs.fileType == '8'
        "
        :src="node.attrs.attrs.fileUrl"
        style="width: 1000px; height: 600px; border: none"
      >
      </iframe>
    </modal>
  </node-view-wrapper>
</template>

<script setup>
import { nodeViewProps, NodeViewWrapper } from '@tiptap/vue-3'
const { node, updateAttributes } = defineProps(nodeViewProps)
import { DeleteIcon, Edit2Icon, FileSearchIcon } from 'tdesign-icons-vue-next'

import Overlapping from '@/components/menus/toolbar/insert/components/linksDialog/overlapping.vue'
import BookNode from '@/components/menus/toolbar/insert/components/linksDialog/thisBookNode.vue'
import Web from '@/components/menus/toolbar/insert/components/linksDialog/web.vue'

const { editor } = useStore()
const validQuoteTypes = ['imageLayout', 'image', 'imageIcon', 'imageInLine']
const overlappingShow = ref(false)
const resourceLibraryShow = ref(false)
const isShow = reactive({
  web: false,
  bookNode: false,
  overlapping: false,
  resourceLibrary: false,
})

// 编辑链接
const editLink = () => {
  switch (node.attrs.type) {
    case 'chaptersInThisBook':
      isShow.bookNode = true
      break
    case 'websiteLink':
      isShow.web = true
      break
    case 'crossReferencing':
      isShow.overlapping = true
      break
    case 'resourceLibrary':
      isShow.resourceLibraryShow = true
      break
  }
}

const update = (data, type) => {
  updateAttributes(data)
  isShow[type] = false
}

// 从资源库更新链接
const resourceLibraryUpdate = (file) => {
  updateAttributes({
    title: file.title,
    type: 'resourceLibrary',
    href: file.fileUrl,
    attrs: file,
  })
}

const openLink = () => {
  switch (node.attrs.type) {
    case 'chaptersInThisBook':
      MessagePlugin.info('功能开发中')
      break

    case 'websiteLink':
      window.open(node.attrs.href, '_blank')
      break

    case 'crossReferencing':
      overlappingShow.value = true
      break

    case 'resourceLibrary':
      resourceLibraryShow.value = true
      break

    default:
      break
  }
}

// 删除链接
const deleteNode = (e) => {
  editor.value?.chain().focus().deleteSelection().run()
}
</script>

<style lang="less" scoped>
.umo-node-links {
  display: inline;
  white-space: nowrap;
  .umo-node-title {
    cursor: pointer;
    color: var(--umo-primary-color);
    white-space: nowrap;
  }

  .link-text-with-icon::after {
    content: '';
    display: inline-block;
    width: 0.9em;
    height: 0.9em;
    margin-left: 2px;
    vertical-align: middle;
    background-image: url('@/assets/icons/link-blue.svg');
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    pointer-events: none;
  }
}

.umo-node-check {
  background-color: var(--td-brand-color-light);
  color: var(--td-brand-color);
}

.umo-node-links-content {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  overflow: hidden;
}
</style>
