<template>
  <node-view-wrapper :id="node.attrs.id" ref="containerRef" class="umo-node-view" :style="nodeStyle"
    @mouseenter="showChild" @mouseleave="cannelHideLayer">
    <div class="umo-node-container umo-hover-shadow umo-select-outline umo-node-audio">
      <div v-show="mousemove" class="icon" @click.stop="keepOpen">
        <t-dropdown class="rc-icon" :tooltip="false" trigger="click">
          <EllipsisIcon />
          <template #dropdown>
            <t-dropdown-item :value="1" @click="openDialogEdit">
              <div style="display: flex; align-items: center">
                <SettingIcon />
                <span style="margin-left: 5px">{{
                  t('insert.image.setting')
                }}</span>
              </div>
            </t-dropdown-item>
            <t-dropdown-item :value="2" @click="handleDelNode">
              <div style="display: flex; align-items: center">
                <DeleteIcon />
                <span style="margin-left: 5px">{{
                  t('insert.image.remove')
                }}</span>
              </div>
            </t-dropdown-item>
          </template>
        </t-dropdown>
      </div>
      <audio ref="audiorRef" :src="node.attrs.src" controls crossorigin="anonymous" preload="metadata"
        @loadedmetadata="loadedmetadata"></audio>
      <div class="umo-node-audio-title __ellipsis">
        {{ node.attrs.audioTitle }}
      </div>
    </div>

    <t-dialog v-model:visible="audioEditPopup" attach="body" :header="t('insert.image.setting')" width="30%"
      :close-on-overlay-click="false" @confirm="onSubmit" @close="onClose" @cancel="onClose">
      <div>
        <t-form ref="formValidatorStatus" :data="formData" :label-width="120">
          <!-- 音频标题-->
          <t-form-item :label="t('insert.fileDialog.audioName')" name="audioTitle">
            <t-input v-model="formData.audioTitle" :placeholder="t('insert.fileDialog.audioTip')"></t-input>
          </t-form-item>
          <!-- 链接地址-->
          <t-form-item :label="t('insert.fileDialog.audioBtn')" name="linkAddress">
            <t-button @click="changeFile">
              {{ t('insert.fileDialog.audioBtn') }}
            </t-button>
          </t-form-item>
        </t-form>
      </div>
    </t-dialog>
  </node-view-wrapper>
</template>

<script setup lang="ts">
import { nodeViewProps, NodeViewWrapper } from '@tiptap/vue-3'
import type { ReactiveVariable } from '@vue-macros/reactivity-transform/macros'
import { DeleteIcon, EllipsisIcon, SettingIcon } from 'tdesign-icons-vue-next'

import { useMouseOver } from '@/hooks/mouseOver'
import { chooseFile } from '@/utils/file'
import { mediaPlayer } from '@/utils/player'

const { node, updateAttributes, deleteNode } = defineProps(nodeViewProps)
const { options, editor } = useStore()
const { cannelHideLayer, showChild, mousemove, keepOpen } = useMouseOver()

const containerRef = ref<HTMLElement | null>(null)
const audiorRef = $ref<ReactiveVariable<HTMLAudioElement> | any | null>(null)
let player = $ref<Plyr | null>(null)
let selected = $ref(false)

const nodeStyle = $computed(() => {
  const { nodeAlign, margin } = node.attrs
  const marginTop =
    margin?.top && margin?.top !== '' ? `${margin.top}px` : undefined
  const marginBottom =
    margin?.bottom && margin?.bottom !== '' ? `${margin.bottom}px` : undefined
  return {
    'justify-content': nodeAlign,
    marginTop,
    marginBottom,
  }
})

let audioEditPopup = $ref(false)

const formData = ref({})

const changeFile = () => {
  chooseFile(
    (file) => {
      formData.value = {
        src: file.fileUrl,
        size: file.fileSize,
        audioTitle: file.originName,
        name: file.originName,
      }
    },
    false,
    '.mp3,.wav',
  )
}

const openDialogEdit = () => {
  audioEditPopup = true
  formData.value = {
    audioTitle: node.attrs.audioTitle,
    src: node.attrs.src,
    size: node.attrs.size,
    name: node.attrs.name,
  }
}

const handleDelNode = () => {
  deleteNode()
}
const onClose = () => {
  audioEditPopup = false
}
const onSubmit = () => {
  if (!formData.value.audioTitle) {
    return
  }
  updateAttributes({
    ...formData.value,
  })
  onClose()
}

onMounted(async () => {
  player = mediaPlayer(audiorRef)
})

const loadedmetadata = () => {
  updateAttributes({
    duration: parseInt(player?.duration),
  })
}

onBeforeUnmount(() => {
  if (player) {
    player?.destroy()
  }
})

onClickOutside(containerRef, () => {
  selected = false
})
</script>

<style lang="less">
.umo-node-view {
  position: relative;
  margin: 20px 0;

  .icon {
    position: absolute;
    right: 0px;
    top: -8px;
    z-index: 99;
    font-size: 24px;
    cursor: pointer;
    background-color: rgba(0, 0, 0, 0.5);
    border-radius: 4px;
    color: #fff;
    padding: 5px;
    width: 24px;
    height: 24px;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .umo-node-audio {
    max-width: 100%;
    width: 100%;
    margin-top: 20px;
    border-radius: var(--umo-radius);
    outline: solid 1px var(--umo-border-color);

    audio {
      width: 100%;
      outline: none;
    }

    .umo-node-audio-title {
      padding: 10px 0;
      text-align: center;
    }

    .uploading {
      position: absolute;
      z-index: 10;
      right: 0;
      top: 0;
      background: rgba(0, 0, 0, 0.2);
      height: 2px;
      left: 0;
      border-top-left-radius: var(--umo-radius);
      border-top-right-radius: var(--umo-radius);

      &:after {
        content: '';
        display: block;
        height: 100%;
        background-color: var(--umo-primary-color);
        animation: progress 1s linear infinite;
      }
    }
  }
}

@keyframes progress {
  0% {
    width: 0;
  }

  100% {
    width: 100%;
  }
}
</style>
